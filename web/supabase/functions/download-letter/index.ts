import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import puppeteer from 'puppeteer'

interface RequestBody {
  letterId: string
}

interface ErrorResponse {
  error: string
}

interface Letter {
  id: string
  user_id: string
  plain_text: string
  design_html: string
  template_id: string
  created_at: string
}

// Template data structure (simplified version)
interface Template {
  id: string
  name: string
}

// Basic template mapping - you may want to sync this with your actual templates
const templateMap: Record<string, Template> = {
  'plain-text': { id: 'plain-text', name: 'Plain_Text' },
  'modern': { id: 'modern', name: 'Modern' },
  'classic': { id: 'classic', name: 'Classic' },
  'professional': { id: 'professional', name: 'Professional' },
  // Add more templates as needed
}

serve(async (req: Request): Promise<Response> => {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { 'Content-Type': 'application/json' } 
      }
    )
  }

  try {
    // Parse request body
    const body: RequestBody = await req.json()
    const { letterId } = body

    if (!letterId) {
      return new Response(
        JSON.stringify({ error: 'Letter ID is required' } as ErrorResponse),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    const browserlessToken = Deno.env.get('BROWSERLESS_IO_TOKEN')

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return new Response(
        JSON.stringify({ error: 'Server configuration error' } as ErrorResponse),
        { 
          status: 500, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!browserlessToken) {
      console.error('BROWSERLESS_IO_TOKEN environment variable is not set')
      return new Response(
        JSON.stringify({ error: 'Server configuration error' } as ErrorResponse),
        { 
          status: 500, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(
        JSON.stringify({ error: 'Authentication required' } as ErrorResponse),
        { 
          status: 401, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    const token = authHeader.replace('Bearer ', '')

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(token)

    if (userError || !user) {
      console.error('Error getting user with token:', userError)
      return new Response(
        JSON.stringify({ error: 'Invalid authentication token' } as ErrorResponse),
        { 
          status: 401, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get the letter from database
    const { data: letter, error: fetchError } = await supabase
      .from('letters')
      .select('id, user_id, plain_text, design_html, template_id, created_at')
      .eq('id', letterId)
      .eq('user_id', user.id) // Ensure user can only access their own letters
      .single()

    if (fetchError || !letter) {
      console.error('Error fetching letter:', fetchError)
      return new Response(
        JSON.stringify({ error: 'Letter not found or access denied' } as ErrorResponse),
        { 
          status: 404, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    const typedLetter = letter as Letter

    if (!typedLetter.design_html) {
      return new Response(
        JSON.stringify({ error: 'Letter has no design HTML content' } as ErrorResponse),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    // Connect to browserless.io using Puppeteer
    console.log('Connecting to browserless.io...')
    const browser = await puppeteer.connect({
      browserWSEndpoint: `wss://production-sfo.browserless.io?token=${browserlessToken}`
    })

    console.log('Creating new page...')
    const page = await browser.newPage()

    // Set viewport size
    await page.setViewport({
      width: 794,  // A4 width in pixels (72 dpi)
      height: 1123, // A4 height in pixels (72 dpi)
    })

    console.log('Setting HTML content...')
    // Set the HTML content
    await page.setContent(typedLetter.design_html)

    // Wait for fonts to be loaded
    await page.evaluate(() => document.fonts.ready)

    console.log('Generating PDF...')
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0',
        right: '0',
        bottom: '0',
        left: '0',
      },
    })

    console.log('Closing browser...')
    // Close browser connection
    await browser.close()

    // Get template name for filename
    const template = templateMap[typedLetter.template_id] || { name: 'Unknown' }
    const fileName = `Surat_Lamaran_Gigsta_${template.name}`

    console.log('PDF generated successfully')
    // Return PDF as response
    return new Response(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${fileName}.pdf"`,
        'Content-Length': pdfBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    })

  } catch (error) {
    console.error('Error in download-letter function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to download letter. Please try again.' 
      } as ErrorResponse),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    )
  }
})
